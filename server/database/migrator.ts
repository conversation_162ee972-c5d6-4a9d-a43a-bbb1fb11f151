import type { Kysely } from 'kysely';

import type { Database } from './types';

/**
 * Migration definition interface
 */
export interface Migration {
    name: string;
    up: (db: <PERSON>ysely<Database>) => Promise<void>;
    down: (db: Kysely<Database>) => Promise<void>;
}

/**
 * Migration runner class
 */
export class Migrator {
    private db: Kysely<any>; // Use any to avoid type conflicts between Database and DatabaseWithMigrations
    private migrations: Array<Migration>;

    constructor(db: Kysely<Database>, migrations: Array<Migration>) {
        this.db = db as any;
        this.migrations = migrations;
    }

    /**
     * Initialize the migrations table if it doesn't exist
     */
    private async ensureMigrationsTable(): Promise<void> {
        await this.db.schema
            .createTable('migrations')
            .ifNotExists()
            .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
            .addColumn('name', 'text', (col) => col.notNull().unique())
            .addColumn('executed_at', 'text', (col) => col.notNull().defaultTo('datetime("now")'))
            .execute()
            .catch(() => {
                // Table might already exist, ignore error
            });
    }

    /**
     * Get list of executed migrations
     */
    private async getExecutedMigrations(): Promise<Array<string>> {
        await this.ensureMigrationsTable();
        
        const result = await this.db
            .selectFrom('migrations')
            .select('name')
            .orderBy('executed_at', 'asc')
            .execute();

        return result.map(row => row.name);
    }

    /**
     * Record a migration as executed
     */
    private async recordMigration(name: string): Promise<void> {
        await this.db
            .insertInto('migrations')
            .values({
                name,
                executed_at: new Date().toISOString()
            })
            .execute();
    }

    /**
     * Remove a migration record
     */
    private async removeMigrationRecord(name: string): Promise<void> {
        await this.db
            .deleteFrom('migrations')
            .where('name', '=', name)
            .execute();
    }

    /**
     * Run all pending migrations
     */
    async migrateUp(): Promise<void> {
        const executedMigrations = await this.getExecutedMigrations();
        const pendingMigrations = this.migrations.filter(
            migration => !executedMigrations.includes(migration.name)
        );

        console.log(`Found ${pendingMigrations.length} pending migrations`);

        for (const migration of pendingMigrations) {
            console.log(`Running migration: ${migration.name}`);
            
            try {
                await migration.up(this.db);
                await this.recordMigration(migration.name);
                console.log(`✓ Migration ${migration.name} completed`);
            } catch (error) {
                console.error(`✗ Migration ${migration.name} failed:`, error);
                throw error;
            }
        }

        if (pendingMigrations.length === 0) {
            console.log('No pending migrations');
        }
    }

    /**
     * Rollback the last migration
     */
    async migrateDown(): Promise<void> {
        const executedMigrations = await this.getExecutedMigrations();
        
        if (executedMigrations.length === 0) {
            console.log('No migrations to rollback');
            return;
        }

        const lastMigration = executedMigrations[executedMigrations.length - 1];
        const migration = this.migrations.find(m => m.name === lastMigration);

        if (!migration) {
            throw new Error(`Migration ${lastMigration} not found in migration files`);
        }

        console.log(`Rolling back migration: ${migration.name}`);

        try {
            await migration.down(this.db);
            await this.removeMigrationRecord(migration.name);
            console.log(`✓ Migration ${migration.name} rolled back`);
        } catch (error) {
            console.error(`✗ Rollback of ${migration.name} failed:`, error);
            throw error;
        }
    }

    /**
     * Get migration status
     */
    async getStatus(): Promise<{ executed: Array<string>; pending: Array<string> }> {
        const executedMigrations = await this.getExecutedMigrations();
        const allMigrationNames = this.migrations.map(m => m.name);
        const pendingMigrations = allMigrationNames.filter(
            name => !executedMigrations.includes(name)
        );

        return {
            executed: executedMigrations,
            pending: pendingMigrations
        };
    }
}

/**
 * Create a migrator instance with all available migrations
 */
export async function createMigrator(db: Kysely<Database>): Promise<Migrator> {
    // Import all migration files
    const migration001 = await import('./migrations/001_create_stocks_manager_events');

    const migrations: Array<Migration> = [
        {
            name: '001_create_stocks_manager_events',
            up: migration001.up,
            down: migration001.down
        }
    ];

    return new Migrator(db, migrations);
}
