<template>
    <AutoComplete
        v-model="internalModel"
        :multiple="multiple"
        :option-label="SELECT_OPTION_LABEL"
        :suggestions="toSelectOptions(filteredSuggestions)"
        fluid
        pt:input:type="search"
        @blur="onBlur"
        @complete="onFilter"
        @before-hide="onHide"
    />
</template>

<script setup lang="ts">
    import { createOptional, MapPlus } from '@ti-platform/aide';
    import { syncRef } from '@vueuse/core';
    import type { AutoCompleteCompleteEvent } from 'primevue';
    import { AutoComplete } from 'primevue';
    import { computed, ref } from 'vue';

    import type { SelectOption } from '~/utils/transformers';
    import { SELECT_OPTION_LABEL, SELECT_OPTION_VALUE, toSelectOptions, toStringMapPlus } from '~/utils/transformers';

    const {
        suggestions,
        forceSelection = false,
        multiple = false,
    } = defineProps<{
        suggestions: MapPlus<string, string> | Array<string>;

        forceSelection?: boolean;
        multiple?: boolean;
    }>();

    const [model, modifiers] = defineModel<string | Array<string>>();

    const internalModel = ref<SelectOption | Array<SelectOption>>();
    const filteredSuggestions = ref<MapPlus<string, string>>(new MapPlus());
    const searchInput = ref<HTMLInputElement>();

    const suggestionsAsMap = computed(() => toStringMapPlus(suggestions));

    syncRef(model, internalModel, {
        transform: {
            ltr: (m) => {
                return createOptional(m)
                    .map((v) => (Array.isArray(v) ? v : [v]))
                    .map((v) =>
                        v.map((i) =>
                            suggestionsAsMap.value.has(i)
                                ? {
                                      [SELECT_OPTION_LABEL]: suggestionsAsMap.value.getOrThrow(i),
                                      [SELECT_OPTION_VALUE]: i,
                                  }
                                : { [SELECT_OPTION_LABEL]: i, [SELECT_OPTION_VALUE]: i }
                        )
                    )
                    .map((v) => (multiple ? v : v.shift()))
                    .orUndefined();
            },

            rtl: (im) => {
                return createOptional(im)
                    .map((v) => (Array.isArray(v) ? v : [v]))
                    .map((v) => v.map((i) => i[SELECT_OPTION_VALUE]))
                    .map((v) => (multiple ? v : v.shift()))
                    .orUndefined();
            },
        },
    });

    function onBlur({ currentTarget }: Event) {
        createOptional(currentTarget).ifPresent((i) => (searchInput.value = i as HTMLInputElement));
    }

    function onFilter(event: AutoCompleteCompleteEvent) {
        const query = event.query.toUpperCase();
        filteredSuggestions.value = suggestionsAsMap.value.filter(({ value }) => value.toUpperCase().includes(query));
    }

    function onHide() {
        if (!forceSelection) {
            return;
        }

        createOptional(searchInput.value)
            .map((i) => i.value.trim())
            .filter((v) => v !== '')
            .map((v) => (modifiers.caps ? v.toUpperCase() : v))
            .ifPresent((v) => {
                model.value = multiple ? [...createOptional(model.value).orElse([]), v] : v;
                searchInput.value!.value = '';
            });
    }
</script>
