import { getDatabase } from './config';
import { createMigrator } from './migrator';

// Re-export types and utilities
export { createDatabase, getDatabase } from './config';
export { createMigrator, Migrator } from './migrator';
export type { Database } from './types';

// Re-export schema types and constants
export type {
    NewStocksManagerEvent,
    StocksManagerEvent,
    StocksManagerEventsTable,
    StocksManagerEventUpdate
} from './schema';
export {
    SCHEMA_TABLE_STOCKS_MANAGER_EVENTS,
    StocksManagerEventType,
    TABLE_NAME_STOCKS_MANAGER_EVENTS} from './schema';

/**
 * Get a database instance ready to use
 * This is the main function to use in your server-side code
 */
export function useDatabase() {
    return getDatabase();
}

/**
 * Run database migrations
 * This should be called during application startup
 */
export async function runMigrations() {
    const db = getDatabase();
    const migrator = await createMigrator(db);
    await migrator.migrateUp();
}

/**
 * Get migration status
 */
export async function getMigrationStatus() {
    const db = getDatabase();
    const migrator = await createMigrator(db);
    return await migrator.getStatus();
}
