import { getDatabase } from './config';
import { createMigrator } from './migrator';

// Re-export types and utilities
export type { Database } from './types';
export { createDatabase, getDatabase } from './config';
export { createMigrator, Migrator } from './migrator';

// Re-export schema types and constants
export type {
    StocksManagerEventsTable,
    StocksManagerEvent,
    NewStocksManagerEvent,
    StocksManagerEventUpdate,
    TableStocksManagerEvents
} from './schema';

export {
    TABLE_NAME_STOCKS_MANAGER_EVENTS,
    StocksManagerEventType,
    SCHEMA_TABLE_STOCKS_MANAGER_EVENTS
} from './schema';

/**
 * Get a database instance ready to use
 * This is the main function to use in your server-side code
 */
export function useDatabase() {
    return getDatabase();
}

/**
 * Run database migrations
 * This should be called during application startup
 */
export async function runMigrations() {
    const db = getDatabase();
    const migrator = await createMigrator(db);
    await migrator.migrateUp();
}

/**
 * Get migration status
 */
export async function getMigrationStatus() {
    const db = getDatabase();
    const migrator = await createMigrator(db);
    return await migrator.getStatus();
}
