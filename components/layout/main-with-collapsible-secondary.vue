<template>
    <div :class="{ flex: isVertical }">
        <div :class="{ 'flex-auto': isVertical }"><slot name="main" /></div>
        <transition-group name="p-toggleable-content">
            <template v-if="isSecondaryVisible">
                <Divider v-if="isVertical" :layout="layout" />
                <div v-bind="secondaryBindings ?? {}">
                    <Divider v-if="!isVertical" :layout="layout" />
                    <slot name="secondary" />
                </div>
            </template>
        </transition-group>
    </div>
</template>

<script setup lang="ts">
    import { Divider } from 'primevue';
    import { computed } from 'vue';

    const {
        isSecondaryVisible = true,
        secondaryBindings = undefined,
        layout = 'horizontal',
    } = defineProps<{
        isSecondaryVisible?: boolean;
        layout?: 'horizontal' | 'vertical';
        secondaryBindings?: unknown;
    }>();

    const isVertical = computed(() => layout === 'vertical');
</script>
