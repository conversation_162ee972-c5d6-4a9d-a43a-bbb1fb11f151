import type { Generated, Insertable, Selectable, Updateable } from 'kysely';
import { z } from 'zod/v4';

// Table names
export const TABLE_NAME_STOCKS_MANAGER_EVENTS = 'stocks_manager_events';

// Enums and constants
export const StocksManagerEventType = {
    DIVIDEND: 'Dividend',
    PURCHASE: 'Purchase',
    REVERSE_SPLIT: 'Reverse Split',
    SALE: 'Sale',
} as const;

// Zod validation schemas
export const SCHEMA_TABLE_STOCKS_MANAGER_EVENTS = z.object({
    id: z.number().transform(i => i as unknown as Generated<number>),
    date: z.iso.date(),
    symbol: z.string().max(10).uppercase(),
    type: z.enum(StocksManagerEventType),
    quantity: z.number().optional().default(0),
    price_per_share: z.number().optional().default(0),
    fees: z.number().optional().default(0),
    total: z.number().optional().default(0),
});

// Kysely table interface (for type-safe queries)
export interface StocksManagerEventsTable {
    id: Generated<number>;
    date: string; // ISO date string
    symbol: string;
    type: 'Dividend' | 'Purchase' | 'Reverse Split' | 'Sale';
    quantity: number;
    price_per_share: number;
    fees: number;
    total: number;
}

// Zod-inferred types (for validation)
export type TableStocksManagerEvents = z.infer<typeof SCHEMA_TABLE_STOCKS_MANAGER_EVENTS>;

// Kysely helper types (for CRUD operations)
export type StocksManagerEvent = Selectable<StocksManagerEventsTable>;
export type NewStocksManagerEvent = Insertable<StocksManagerEventsTable>;
export type StocksManagerEventUpdate = Updateable<StocksManagerEventsTable>;
