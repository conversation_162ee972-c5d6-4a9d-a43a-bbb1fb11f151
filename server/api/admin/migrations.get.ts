import { getMigrationStatus } from '~/server/database';

/**
 * GET /api/admin/migrations
 * Get the status of database migrations
 */
export default defineEventHandler(async (event) => {
    try {
        const status = await getMigrationStatus();
        
        return {
            success: true,
            data: {
                executed: status.executed,
                pending: status.pending,
                total_migrations: status.executed.length + status.pending.length,
                is_up_to_date: status.pending.length === 0
            }
        };
    } catch (error) {
        console.error('Failed to get migration status:', error);
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to get migration status',
            data: {
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
});
