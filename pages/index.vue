<template>
    <StandardPanelWithHeader header="Welcome">
        <Select v-model="selectedOption" :options="options" option-label="label" option-value="value" />
        <MultiSelect v-model="selectedOptions" :options="options" option-label="label" option-value="value" />
    </StandardPanelWithHeader>
</template>

<script setup lang="ts">
    import { MultiSelect, Select } from 'primevue';
    import { ref } from 'vue';

    import { StandardPanelWithHeader } from '#components';
    import { definePageMeta } from '#imports';

    definePageMeta({
        key: 'index',
        title: 'Home',
    });

    const options = [
        { label: 'Option 1', value: '1' },
        { label: 'Option 2', value: '2' },
        { label: 'Option 3', value: '3' },
    ];

    const selectedOption = ref(null);
    const selectedOptions = ref([]);
</script>
