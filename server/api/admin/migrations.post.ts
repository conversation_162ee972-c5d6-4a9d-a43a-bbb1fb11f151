import { createMigrator, runMigrations, useDatabase } from '~/server/database';

/**
 * POST /api/admin/migrations
 * Manually trigger database migrations
 * Body: { action: 'up' | 'down' }
 */
export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const action = body?.action || 'up';
        
        if (!['up', 'down'].includes(action)) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Invalid action. Use "up" or "down"'
            });
        }
        
        const db = useDatabase();
        const migrator = await createMigrator(db);
        
        let result;
        
        if (action === 'up') {
            await migrator.migrateUp();
            result = { message: 'Migrations executed successfully' };
        } else {
            await migrator.migrateDown();
            result = { message: 'Last migration rolled back successfully' };
        }
        
        // Get updated status
        const status = await migrator.getStatus();
        
        return {
            success: true,
            data: {
                action,
                ...result,
                status: {
                    executed: status.executed,
                    pending: status.pending,
                    total_migrations: status.executed.length + status.pending.length,
                    is_up_to_date: status.pending.length === 0
                }
            }
        };
    } catch (error) {
        console.error('Migration operation failed:', error);
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Migration operation failed',
            data: {
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
});
