import type { Kysely } from 'kysely';
import type { Database } from '../types';

/**
 * Migration: Create stocks_manager_events table
 * This migration creates the main table for tracking stock management events
 */
export async function up(db: Kysely<Database>): Promise<void> {
    await db.schema
        .createTable('stocks_manager_events')
        .addColumn('id', 'integer', (col) => col.primaryKey().autoIncrement())
        .addColumn('date', 'text', (col) => col.notNull()) // ISO date string
        .addColumn('symbol', 'text', (col) => col.notNull())
        .addColumn('type', 'text', (col) => col.notNull())
        .addColumn('quantity', 'real', (col) => col.notNull().defaultTo(0))
        .addColumn('price_per_share', 'real', (col) => col.notNull().defaultTo(0))
        .addColumn('fees', 'real', (col) => col.notNull().defaultTo(0))
        .addColumn('total', 'real', (col) => col.notNull().defaultTo(0))
        .execute();

    // Create indexes for better query performance
    await db.schema
        .createIndex('idx_stocks_manager_events_symbol')
        .on('stocks_manager_events')
        .column('symbol')
        .execute();

    await db.schema
        .createIndex('idx_stocks_manager_events_date')
        .on('stocks_manager_events')
        .column('date')
        .execute();

    await db.schema
        .createIndex('idx_stocks_manager_events_type')
        .on('stocks_manager_events')
        .column('type')
        .execute();

    // Add check constraints for valid event types
    await db.schema
        .alterTable('stocks_manager_events')
        .addCheckConstraint(
            'chk_stocks_manager_events_type',
            'type IN ("Dividend", "Purchase", "Reverse Split", "Sale")'
        )
        .execute();

    // Add check constraint for symbol length (max 10 characters)
    await db.schema
        .alterTable('stocks_manager_events')
        .addCheckConstraint(
            'chk_stocks_manager_events_symbol_length',
            'length(symbol) <= 10'
        )
        .execute();
}

/**
 * Rollback migration: Drop stocks_manager_events table
 */
export async function down(db: Kysely<Database>): Promise<void> {
    await db.schema.dropTable('stocks_manager_events').execute();
}
