# Database Setup with <PERSON><PERSON><PERSON> and D1

This project uses <PERSON><PERSON><PERSON> as the query builder with Cloudflare D1 as the database backend.

## Architecture

- **Kysely**: Type-safe SQL query builder
- **D1**: Cloudflare's SQLite-based database
- **kysely-d1**: Dialect adapter for <PERSON><PERSON><PERSON> to work with D1

### Schema Organization

- **`schema.ts`**: Contains all table definitions, validation schemas, and type exports
  - Kysely table interfaces for type-safe queries
  - Zod validation schemas for input validation
  - Helper types for CRUD operations
  - Table names and enum constants
- **`types.ts`**: Contains the main Database interface and migration-related types
- **`config.ts`**: Database connection and configuration
- **`migrator.ts`**: Migration system implementation

## File Structure

```
server/
├── database/
│   ├── config.ts           # Database configuration and connection
│   ├── types.ts            # Database interface and migration types
│   ├── schema.ts           # Table schemas, validation, and type definitions
│   ├── migrator.ts         # Migration runner utility
│   ├── index.ts            # Main database exports
│   └── migrations/
│       └── 001_create_stocks_manager_events.ts
├── plugins/
│   └── database.ts         # Nitro plugin for automatic migration on startup
├── api/
│   ├── stocks/
│   │   ├── events.get.ts   # GET /api/stocks/events
│   │   └── events.post.ts  # POST /api/stocks/events
│   ├── admin/
│   │   ├── migrations.get.ts # GET /api/admin/migrations
│   │   └── migrations.post.ts # POST /api/admin/migrations
│   └── health/
│       └── database.get.ts # GET /api/health/database
```

## Setup Instructions

### 1. Cloudflare D1 Database Setup

First, create a D1 database in your Cloudflare account:

```bash
# Install Wrangler CLI
npm install -g wrangler

# Login to Cloudflare
wrangler login

# Create a new D1 database
wrangler d1 create finance-manager-db
```

### 2. Update wrangler.toml

Update the `database_id` in `wrangler.toml` with the ID from the previous step:

```toml
[[d1_databases]]
binding = "DB"
database_name = "finance-manager-db"
database_id = "your-actual-database-id-here"
```

### 3. Local Development

For local development, you can use Wrangler's local D1 emulation:

```bash
# Start local D1 database
wrangler d1 execute finance-manager-db --local --command "SELECT 1"

# Run migrations locally (mock mode)
npx tsx scripts/migrate.ts up

# Start development server
npm run dev
```

### 4. Production Deployment

Deploy to Cloudflare Pages with D1:

```bash
# Build the application
npm run build

# Deploy to Cloudflare Pages
wrangler pages deploy .output/public

# Run migrations in production
# (Migrations will run automatically on startup if RUN_MIGRATIONS=true)
```

## Usage Examples

### Basic Database Operations

```typescript
import { useDatabase, TABLE_NAME_STOCKS_MANAGER_EVENTS } from '~/server/database';

// Get database instance
const db = useDatabase();

// Select all events
const events = await db
    .selectFrom(TABLE_NAME_STOCKS_MANAGER_EVENTS)
    .selectAll()
    .execute();

// Insert new event
const newEvent = await db
    .insertInto(TABLE_NAME_STOCKS_MANAGER_EVENTS)
    .values({
        date: '2024-01-15',
        symbol: 'AAPL',
        type: 'Purchase',
        quantity: 100,
        price_per_share: 150.00,
        fees: 9.99,
        total: 15009.99
    })
    .returningAll()
    .executeTakeFirstOrThrow();
```

### API Endpoints

- `GET /api/stocks/events` - Retrieve all stock events
- `POST /api/stocks/events` - Create a new stock event
- `GET /api/admin/migrations` - Check migration status
- `POST /api/admin/migrations` - Run migrations manually (body: `{ "action": "up" | "down" }`)
- `GET /api/health/database` - Check database connection and health

### Migration Management

```bash
# Check migration status
npx tsx scripts/migrate.ts status

# Run pending migrations
npx tsx scripts/migrate.ts up

# Rollback last migration
npx tsx scripts/migrate.ts down
```

## Environment Variables

- `NODE_ENV`: Environment mode (development/production)
- `RUN_MIGRATIONS`: Set to "true" to run migrations on startup

## Database Schema

The `stocks_manager_events` table includes:

- `id`: Auto-incrementing primary key
- `date`: ISO date string
- `symbol`: Stock symbol (max 10 characters)
- `type`: Event type (Dividend, Purchase, Reverse Split, Sale)
- `quantity`: Number of shares
- `price_per_share`: Price per share
- `fees`: Transaction fees
- `total`: Total transaction amount

## Type Safety

All database operations are fully type-safe thanks to Kysely and TypeScript:

```typescript
// TypeScript will catch errors at compile time
const event = await db
    .selectFrom('stocks_manager_events')
    .select(['id', 'symbol', 'invalid_column']) // ❌ Error: invalid_column doesn't exist
    .execute();
```

## Troubleshooting

### Common Issues

1. **D1 binding not found**: Make sure your `wrangler.toml` is configured correctly and you're running in a Cloudflare Workers environment.

2. **Migration errors**: Check the database connection and ensure the D1 database exists.

3. **Type errors**: Ensure your database schema types match the actual database structure.

### Development Tips

- Use the mock database for local development and testing
- Always run migrations before deploying to production
- Check migration status with the admin API endpoint
- Use the Zod schema for input validation in API endpoints
