import type { Generated } from 'kysely';

import type { StocksManagerEventsTable } from './schema';

/**
 * SQLite system table for metadata queries
 */
export interface SqliteMasterTable {
    type: string;
    name: string;
    tbl_name: string;
    rootpage: number;
    sql: string;
}

/**
 * Database schema interface for Kysely
 * This defines the structure of all tables in the database
 */
export interface Database {
    stocks_manager_events: StocksManagerEventsTable;
    sqlite_master: SqliteMasterTable; // SQLite system table for metadata queries
}

/**
 * Migration table for tracking applied migrations
 */
export interface MigrationsTable {
    id: Generated<number>;
    name: string;
    executed_at: Generated<string>; // ISO timestamp
}

/**
 * Extended database interface including migration table
 */
export interface DatabaseWithMigrations extends Database {
    migrations: MigrationsTable;
}
