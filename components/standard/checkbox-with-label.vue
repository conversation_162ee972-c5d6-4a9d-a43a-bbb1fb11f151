<template>
    <div class="flex gap-x-2">
        <label class="cursor-pointer">
            <Checkbox ref="checkbox" v-model="model" :indeterminate="indeterminate" class="shrink" binary />
            <slot name="label">
                <span>{{ label }}</span>
            </slot>
        </label>
    </div>
</template>

<script setup lang="ts">
    import { Checkbox } from 'primevue';
    import type { Ref } from 'vue';
    import { onMounted, ref, watch } from 'vue';

    const model = defineModel<boolean>({ default: false });

    const { indeterminate = false, label = '' } = defineProps<{
        indeterminate?: boolean;
        label?: string;
    }>();

    const checkbox = ref() as Ref<{ $data: { d_indeterminate: boolean } }>;

    onMounted(() => {
        watch(model, (newValue: boolean, previousValue: boolean | undefined) => {
            if (!indeterminate) {
                return;
            }

            if (previousValue === false && newValue === true) {
                (model as Ref<boolean | undefined>).value = undefined;
                checkbox.value.$data.d_indeterminate = true;
            }
        });
    });
</script>
