import { TABLE_NAME_STOCKS_MANAGER_EVENTS,useDatabase } from '~/server/database';

/**
 * GET /api/stocks/events
 * Retrieve all stock manager events
 */
export default defineEventHandler(async (event) => {
    try {
        const db = useDatabase();
        
        const events = await db
            .selectFrom(TABLE_NAME_STOCKS_MANAGER_EVENTS)
            .selectAll()
            .orderBy('date', 'desc')
            .execute();

        return {
            success: true,
            data: events,
            count: events.length
        };
    } catch (error) {
        console.error('Failed to fetch stock events:', error);
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to fetch stock events',
            data: {
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
});
