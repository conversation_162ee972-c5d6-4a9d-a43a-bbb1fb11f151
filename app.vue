<template>
    <TopbarWithSidebarLayout
        v-model:is-sidebar-visible="isSidebarVisible"
        :class="[layoutVisibility, 'transition-all', 'transition-duration-1000']"
        :menu-items="menuItems"
        pt:header:class="z-10 drop-shadow-md py-1 px-4 bg-primary text-white"
        pt:sidebar-toggle:class="text-white"
        pt:sidebar:class="bg-surface-50 drop-shadow-md p-4"
        pt:main-content:class="p-4"
        expanded-menu-keys
    >
        <template #header-content>Finance Manager</template>
        <template #main-content>
            <NuxtPage />
        </template>
    </TopbarWithSidebarLayout>
</template>

<script setup lang="ts">
    import { TopbarWithSidebarLayout } from '@ti-platform/aide-primevue';
    import type { MenuItem } from 'primevue/menuitem';
    import { onMounted, ref } from 'vue';
    import { useRouter } from 'vue-router';

    import { NuxtPage } from '#components';
    import { navigateTo } from '#imports';

    const router = useRouter();

    function enrichMenuItems(items: Array<MenuItem>): Array<MenuItem> {
        return items.map((item) => {
            if (!item.key) {
                throw new Error('Menu item must have a key');
            }

            const route = router.hasRoute(item.key)
                ? router.resolve({ name: item.key })
                : {
                      name: undefined,
                      path: undefined,
                      meta: { title: item.label },
                  };

            return {
                ...route,
                key: item.key,
                label: route.meta.title as string,
                url: route.path,
                items: item.items ? enrichMenuItems(item.items) : undefined,
                command: async (event) => {
                    if (route.name) {
                        event.originalEvent.preventDefault();
                        await navigateTo({ name: route.name });
                    }
                },
            };
        });
    }

    const layoutVisibility = ref('opacity-0');
    const isSidebarVisible = ref(true);
    const menuItems: Array<MenuItem> = enrichMenuItems([{ key: 'index' }, { key: 'overview' }, { key: 'events' }]);

    onMounted(() => {
        layoutVisibility.value = 'opacity-100';
    });
</script>

<style lang="css">
    @import 'primeicons/primeicons.css';
    @import 'tailwindcss';
    @import 'tailwindcss-primeui';

    @theme {
        --color-error: var(--p-form-field-invalid-border-color);
    }
</style>
