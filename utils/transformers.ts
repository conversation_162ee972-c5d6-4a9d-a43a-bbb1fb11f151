import type { MapPlus } from '@ti-platform/aide';
import { toMapPlus } from '@ti-platform/aide';

export const SELECT_OPTION_LABEL = 'l';
export const SELECT_OPTION_VALUE = 'v';

export type SelectOption = {
    [SELECT_OPTION_LABEL]: string;
    [SELECT_OPTION_VALUE]: string;
};

export function toSelectOptions(options: MapPlus<string, string> | Array<string>): Array<SelectOption> {
    return toStringMapPlus(options)
        .mapValues(({ key, value }) => ({ [SELECT_OPTION_LABEL]: value, [SELECT_OPTION_VALUE]: key }))
        .valuesArray();
}

/**
 * Ensures the input (either an array of strings or a MapPlus) is a MapPlus.
 * For array inputs, each string becomes both the key and value.
 * For MapPlus inputs, returns the input as-is.
 */
export function toStringMapPlus(input: MapPlus<string, string> | Array<string>): MapPlus<string, string> {
    return toMapPlus<string, string>(Array.isArray(input) ? input.map((i): [string, string] => [i, i]) : input);
}
