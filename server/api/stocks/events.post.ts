import { SCHEMA_TABLE_STOCKS_MANAGER_EVENTS,TABLE_NAME_STOCKS_MANAGER_EVENTS, useDatabase } from '~/server/database';

/**
 * POST /api/stocks/events
 * Create a new stock manager event
 */
export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        
        // Validate the input using the Zod schema
        const validatedData = SCHEMA_TABLE_STOCKS_MANAGER_EVENTS.parse(body);
        
        const db = useDatabase();
        
        // Insert the new event
        const result = await db
            .insertInto(TABLE_NAME_STOCKS_MANAGER_EVENTS)
            .values({
                date: validatedData.date,
                symbol: validatedData.symbol,
                type: validatedData.type,
                quantity: validatedData.quantity,
                price_per_share: validatedData.price_per_share,
                fees: validatedData.fees,
                total: validatedData.total
            })
            .returningAll()
            .executeTakeFirstOrThrow();

        return {
            success: true,
            data: result,
            message: 'Stock event created successfully'
        };
    } catch (error) {
        console.error('Failed to create stock event:', error);
        
        // Handle validation errors
        if (error && typeof error === 'object' && 'issues' in error) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Validation failed',
                data: {
                    error: 'Invalid input data',
                    issues: (error as any).issues
                }
            });
        }
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to create stock event',
            data: {
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
});
