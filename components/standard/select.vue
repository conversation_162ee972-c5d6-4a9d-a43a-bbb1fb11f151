<template>
    <Select
        v-model="model"
        :options="toSelectOptions(options)"
        :option-label="SELECT_OPTION_LABEL"
        :option-value="SELECT_OPTION_VALUE"
        fluid
    />
</template>

<script setup lang="ts">
    import type { MapPlus } from '@ti-platform/aide';
    import { Select } from 'primevue';

    import { SELECT_OPTION_LABEL, SELECT_OPTION_VALUE, toSelectOptions } from '~/utils/transformers';

    const model = defineModel<string>();

    const { options } = defineProps<{
        options: MapPlus<string, string> | Array<string>;
    }>();
</script>
