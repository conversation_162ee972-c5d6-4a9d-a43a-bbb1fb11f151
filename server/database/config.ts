import { Kysely } from 'kysely';
import { D1Dialect } from 'kysely-d1';

import type { Database } from './types';

/**
 * Creates a Kysely database instance with D1 dialect
 * @param d1Database - The D1 database binding from Cloudflare Workers
 * @returns Configured Kysely instance
 */
export function createDatabase(d1Database: D1Database): Kysely<Database> {
    return new Kysely<Database>({
        dialect: new D1Dialect({ database: d1Database }),
        log: (event) => {
            // Log queries in development
            if (process.env.NODE_ENV === 'development') {
                console.log('Query:', event.query.sql);
                console.log('Parameters:', event.query.parameters);
                console.log('Duration:', event.queryDurationMillis, 'ms');
            }
        },
    });
}

/**
 * Get the database instance from the Nuxt context
 * This function should be used in server-side code to access the database
 */
export function getDatabase(): Kysely<Database> {
    // In Nuxt 3 with <PERSON><PERSON>, we can access the D1 binding through the global context
    // This will be available when deployed to Cloudflare Workers
    const d1 = (globalThis as any).DB as D1Database;
    
    if (!d1) {
        throw new Error('D1 database binding not found. Make sure DB is configured in your Cloudflare Workers environment.');
    }
    
    return createDatabase(d1);
}

/**
 * Type definition for D1 database binding
 * This matches the Cloudflare Workers D1 API
 */
declare global {
    interface D1Database {
        prepare(query: string): D1PreparedStatement;
        dump(): Promise<ArrayBuffer>;
        batch<T = unknown>(statements: Array<D1PreparedStatement>): Promise<Array<D1Result<T>>>;
        exec(query: string): Promise<D1ExecResult>;
    }

    interface D1PreparedStatement {
        bind(...values: Array<any>): D1PreparedStatement;
        first<T = unknown>(colName?: string): Promise<T | null>;
        run(): Promise<D1Result>;
        all<T = unknown>(): Promise<D1Result<T>>;
        raw<T = unknown>(): Promise<Array<T>>;
    }

    interface D1Result<T = unknown> {
        results?: Array<T>;
        success: boolean;
        error?: string;
        meta: {
            duration: number;
            size_after: number;
            rows_read: number;
            rows_written: number;
        };
    }

    interface D1ExecResult {
        count: number;
        duration: number;
    }
}
