<template>
    <MultiSelectExtended
        v-model="model"
        :options="toSelectOptions(options)"
        :option-label="SELECT_OPTION_LABEL"
        :option-value="SELECT_OPTION_VALUE"
        display="chip"
        toggle-all-label="Select All"
        fluid
        pt:header:class="border-b-2 pb-4"
    />
</template>

<script setup lang="ts">
    import type { MapPlus } from '@ti-platform/aide';
    import { MultiSelectExtended } from '@ti-platform/aide-primevue';

    import { SELECT_OPTION_LABEL, SELECT_OPTION_VALUE, toSelectOptions } from '~/utils/transformers';

    const model = defineModel<Array<string>>();

    const { options } = defineProps<{
        options: MapPlus<string, string> | Array<string>;
    }>();
</script>
